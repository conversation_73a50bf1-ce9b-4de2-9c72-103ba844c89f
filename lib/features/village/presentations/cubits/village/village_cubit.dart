
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gotcha_mfg_app/core/usecase/usecase.dart';
import 'package:gotcha_mfg_app/features/village/domain/usecases/get_village_user.dart';
import '../../../data/models/add_village_user.dart';
import '../../../data/models/update_village_user.dart';
import '../../../domain/usecases/add_village_user.dart';
import '../../../domain/usecases/delete_village_usecase.dart';
import '../../../domain/usecases/update_user_usecase.dart';
import 'village_state.dart';

class VillageCubit extends Cubit<VillageState> {
  VillageCubit(this._getVillageUserUseCase, this._addVillageUserUseCase,
      this._updateVillageUserUseCase, this._deleteVillageUserUseCase)
      : super(VillageInitial());

  final GetVillageUserUseCase _getVillageUserUseCase;
  final AddVillageUserUseCase _addVillageUserUseCase;
  final UpdateVillageUserUseCase _updateVillageUserUseCase;
  final DeleteVillageUserUseCase _deleteVillageUserUseCase;

  Future<void> getVillageUser() async {
    emit(VillageLoading());
    final result = await _getVillageUserUseCase.call(NoParams());

    if (result.isSuccess) {
      emit(VillageLoaded(result.data!));
    } else {
      emit(VillageError(result.error!));
    }
  }

  Future<void> addVillageUser(AddVillageUser request) async {
    emit(VillageLoading());
    final result = await _addVillageUserUseCase.call(request);
    final dataresult = await _getVillageUserUseCase.call(NoParams());

    if (result.isSuccess) {
      emit(VillageUserAdded(result.data!));
      emit(VillageLoaded(dataresult.data!));
    } else {
      emit(VillageError(result.error!));
    }
  }

  Future<void> updateVillageUser(UpdateVillageUser request) async {
    emit(VillageLoading());
    final result = await _updateVillageUserUseCase.call(request);
    final dataresult = await _getVillageUserUseCase.call(NoParams());

    if (result.isSuccess) {
      emit(VillageUserAdded(result.data!));
      emit(VillageLoaded(dataresult.data!));
      // Assuming a success state is needed
    } else {
      emit(VillageError(result.error!));
    }
  }

  Future<void> deleteVillageUser(String id) async {
    emit(VillageLoading());
    final result = await _deleteVillageUserUseCase.call(id);
    final dataresult = await _getVillageUserUseCase.call(NoParams());

    if (result.isSuccess) {
      emit(VillageUserAdded(result.data!));
      emit(VillageLoaded(dataresult.data!));
    } else {
      emit(VillageError(result.error!));
    }
  }
}
