import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gap/gap.dart';
import 'package:gotcha_mfg_app/config/theme/app_colors.dart';
import 'package:gotcha_mfg_app/core/extensions/app_text_theme_extension.dart';
import 'package:gotcha_mfg_app/core/utils/snackbar_service.dart';
import 'package:gotcha_mfg_app/features/profile/presentation/cubits/profile_links/profile_links_cubit.dart';
import 'package:gotcha_mfg_app/shared/widgets/app_header.dart';
import 'package:gotcha_mfg_app/shared/widgets/loading_widget.dart';
import 'package:gotcha_mfg_app/shared/widgets/primary_button.dart';

import '../../../../core/mixpanel_service.dart';
import '../../../../core/utils/platform_utils.dart';
import '../../../../locator.dart';
import '../../../../shared/widgets/retry_widget.dart';
import '../../../../shared/widgets/show_info.dart';
import '../../../profile/data/models/profile_edit_request.dart';
import '../../../profile/presentation/cubits/profile/profile_cubit.dart';
import '../../../profile/presentation/widgets/custom_chip.dart';
import '../widgets/auth_text_field.dart';

@RoutePage()
class PersonaliseAccountPage extends StatefulWidget {
  final bool? isPersonalise;
  final bool? isInfo;
  const PersonaliseAccountPage({
    super.key,
    required this.isPersonalise,
    required this.isInfo,
  });

  @override
  State<PersonaliseAccountPage> createState() => _PersonaliseAccountPageState();
}

class _PersonaliseAccountPageState extends State<PersonaliseAccountPage> {
  TextEditingController firstNameController = TextEditingController();
  TextEditingController lastNameController = TextEditingController();
  TextEditingController ageController = TextEditingController();
  TextEditingController customGenderController = TextEditingController();
  String? _selectedGender;
  String? _otherValue;
  List<String?> selectedIds = [];
  List<String?> postIds = [];
  List sel = [];

  String? firstNameError;
  String? lastNameError;
  String? ageError;
  bool isFormValid = false;

  @override
  void initState() {
    super.initState();
    sl<MixpanelService>().trackScreenView(
      'Personalise Account Page',
      properties: {'Code': 'screen_view.personalise_account_page'},
    );
    context.read<ProfileCubit>().getProfileDetail();
    postIds = [];
  }

  void _validateForm() {
    setState(() {
      isFormValid = _isFirstNameValid();
    });
  }

  bool _isFirstNameValid() {
    if (firstNameController.text.isEmpty) {
      firstNameError = "First name is required";
      return false;
    } else {
      firstNameError = null;
      return true;
    }
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final size = MediaQuery.of(context).size;
    return BlocConsumer<ProfileCubit, ProfileState>(listener: (context, state) {
      if (state is ProfileError) {
        SnackBarService.error(
          context: context,
          message: state.message,
        );
      }
      if (state is ProfileDetailsLoaded) {
        final profile = state.profileResponse.data;
        firstNameController.text = profile?.firstName ?? '';
        lastNameController.text = profile?.lastName ?? '';
        ageController.text = profile?.age ?? '';
        _selectedGender = profile?.gender;
        selectedIds = profile?.identityGroup ?? [];
        _otherValue = state.profileResponse.data?.otherDemographics;
        customGenderController.text =
            state.profileResponse.data?.otherGender ?? '';
      }
      if (state is ProfileUpdated) {
        context.read<ProfileLinksCubit>().getProfileLinks();
        Navigator.pop(context);
      }
    }, builder: (context, state) {
      var size = MediaQuery.of(context).size;

      final isKeyboardOpen = MediaQuery.of(context).viewInsets.bottom != 0;
      if (state is ProfileLoading) {
        return SizedBox(
          height: size.height,
          child: const LoadingWidget(color: Colors.white),
        );
      }
      if (state is ProfileError) {
        return RetryWidget(
          onRetry: () => context.read<ProfileCubit>().getProfileDetail(),
          color: Colors.white,
        );
      }
      if (state is ProfileDetailsLoaded) {
        final profile = state.profileResponse.data;
        final identity = state.identityResponse.data;
        List<String?> matching = identity!
            .where((datum) =>
                profile?.identityGroup?.contains(datum.name) ?? false)
            .map((datum) => datum.id)
            .toList();

        return Scaffold(
          appBar: AppBar(
            toolbarHeight: 0,
            elevation: 0,
            systemOverlayStyle: const SystemUiOverlayStyle(
              statusBarColor: Colors.white,
              systemNavigationBarIconBrightness: Brightness.dark,
              statusBarBrightness: Brightness.light,
              statusBarIconBrightness: Brightness.dark,
              systemNavigationBarColor: AppColors.grey,
            ),
          ),
          body: GestureDetector(
            onTap: () {
              FocusScope.of(context).unfocus();
            },
            behavior: HitTestBehavior.translucent,
            child: Padding(
              padding: EdgeInsets.only(
                top: isIos ? 4 : 8,
                right: 8,
                left: 8,
              ),
              child: Column(
                children: [
                  AppHeader(
                    title: widget.isPersonalise == true
                        ? 'Personalise account'
                        : 'Account details',
                    onBackTap: () {
                      Navigator.pop(context);
                    },
                  ),
                  Expanded(
                    child: Container(
                      color: AppColors.navy,
                      child: Container(
                        decoration: const BoxDecoration(
                          borderRadius: BorderRadius.only(
                            topLeft: Radius.circular(30),
                            topRight: Radius.circular(30),
                          ),
                          color: AppColors.grey,
                        ),
                        child: Padding(
                          padding:
                              EdgeInsets.fromLTRB(24, 24, 24, isIos ? 72 : 56),
                          child: ListView(
                            children: [
                              const Gap(12),
                              if (widget.isInfo == true)
                                const InfoMessage(
                                  color: AppColors.lightRed,
                                  message:
                                      'This helps us personalise your experience and recommend you relevant exercises.',
                                ),
                              if (widget.isInfo == true) const Gap(12),
                              AuthTextField(
                                controller: firstNameController,
                                text: 'First name*',
                                hinttext: 'Enter your first name',
                                errorText: firstNameError,
                                onChanged: (val) {
                                  _isFirstNameValid();
                                  _validateForm();
                                  setState(() {});
                                },
                              ),
                              const Gap(16),
                              AuthTextField(
                                controller: lastNameController,
                                text: 'Last name',
                                hinttext: 'Enter your last name',
                                errorText: lastNameError,
                                onChanged: (val) {
                                  _validateForm();
                                  setState(() {});
                                },
                              ),
                              const Gap(16),
                              Text(
                                'Gender',
                                style: textTheme.bodyRegular.copyWith(
                                  color: AppColors.navy,
                                ),
                              ),
                              const Gap(16),
                              CustomDropdown<String>(
                                value: _selectedGender,
                                hint: 'Optional',
                                items: const [
                                  DropdownMenuItemData(
                                      value: 'Male', label: 'Male'),
                                  DropdownMenuItemData(
                                      value: 'Female', label: 'Female'),
                                  DropdownMenuItemData(
                                      value: 'Non-Binary', label: 'Non-Binary'),
                                  DropdownMenuItemData(
                                      value: 'Prefer to self-describe',
                                      label: 'Prefer to self-describe'),
                                  DropdownMenuItemData(
                                      value: 'Prefer not to say',
                                      label: 'Prefer not to say'),
                                ],
                                onChanged: (newValue) {
                                  setState(() {
                                    _selectedGender = newValue;
                                  });
                                  _validateForm();
                                },
                              ),
                              if (_selectedGender == 'Prefer to self-describe')
                                const Gap(16),

                              if (_selectedGender == 'Prefer to self-describe')
                                AuthTextField(
                                  controller: customGenderController,

                                  text: 'Enter gender details',
                                  hinttext: '',
                                  errorText: ageError,
                                  onChanged: (val) {
                                    _validateForm();
                                    setState(() {});
                                  },
                                ),
                              const Gap(16),
                              AuthTextField(
                                controller: ageController,
                                keyboardType: TextInputType.number,
                                text: 'Age',
                                hinttext: '',
                                errorText: ageError,
                                onChanged: (val) {
                                  _validateForm();
                                  setState(() {});
                                },
                              ),
                              const Gap(20),
                              Text(
                                'Tell us about yourself (optional)',
                                style: textTheme.ralewayRegular.copyWith(
                                  fontSize: 17,
                                  color: AppColors.navy,
                                ),
                              ),
                              const Gap(12),
                              Wrap(
                                spacing: 8,
                                runSpacing: 8,
                                children: [
                                  ActionChipList(
                                    selectedIds: matching.cast<String>(),
                                    isSelect: true,
                                    data: identity ?? [],
                                    showOtherOption: true,
                                    initialOtherValue: _otherValue,
                                    onSelectionChanged: (selected, otherValue) {
                                      setState(() {
                                        print('hi----$otherValue');
                                        print('hi222----$selected');
                                        _otherValue = otherValue;
                                        postIds = selected;
                                        _validateForm();
                                      });
                                    },
                                  ),
                                ],
                              ),


                              const Gap(120),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
          floatingActionButtonLocation:
              FloatingActionButtonLocation.centerDocked,
          floatingActionButton: isKeyboardOpen
              ? const SizedBox()
              : Padding(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 32, vertical: 24),
                  child: SizedBox(
                    width: size.width,
                    child: PrimaryButton(
                      text: widget.isPersonalise == true
                          ? 'Complete profile'
                          : "Save",
                      isEnabled: isFormValid,
                      onPressed: () {
                        if (!_isFirstNameValid()) {
                          _validateForm();
                          return;
                        }
                        var params = UpdateProfileParams(
                          otherDemographics:
                              _otherValue == '' ? null : _otherValue,
                          otherGender:
                              (_selectedGender == 'Prefer to self-describe')
                                  ? customGenderController.text
                                  : null,
                          firstName: firstNameController.text,
                          lastName:
                              lastNameController.text.trim().toString().isEmpty
                                  ? null
                                  : lastNameController.text.toString(),
                          age: ageController.text.trim().toString().isEmpty
                              ? null
                              : ageController.text.toString(),
                          gender: _selectedGender,
                          identityGroup: postIds.isEmpty
                              ? matching.cast<String>()
                              : postIds,
                        );
                        context.read<ProfileCubit>().updateProfile(params);
                        sl<MixpanelService>()
                            .trackButtonClick('Save', properties: {
                          'Page': 'Personalise Account Page',
                          'Code': 'click.personalise_account_page.save',
                          'Data': params.toJson(),
                        });
                      },
                    ),
                  ),
                ),
        );
      }
      return const SizedBox();
    });
  }
}

class DropdownMenuItemData<T> {
  final T value;
  final String label;
  const DropdownMenuItemData({required this.value, required this.label});
}

class CustomDropdown<T> extends StatefulWidget {
  final T? value;
  final String hint;
  final List<DropdownMenuItemData<T>> items;
  final ValueChanged<T?>? onChanged;

  const CustomDropdown({
    super.key,
    this.value,
    required this.hint,
    required this.items,
    this.onChanged,
  });

  @override
  CustomDropdownState<T> createState() => CustomDropdownState<T>();
}

class CustomDropdownState<T> extends State<CustomDropdown<T>> {
  final LayerLink _layerLink = LayerLink();
  OverlayEntry? _overlayEntry;
  bool _isOpen = false;
  final FocusNode _focusNode = FocusNode();

  @override
  void initState() {
    super.initState();
    _focusNode.addListener(_handleFocusChange);
  }

  @override
  void dispose() {
    _focusNode.removeListener(_handleFocusChange);
    _focusNode.dispose();
    super.dispose();
  }

  void _handleFocusChange() {
    if (_focusNode.hasFocus && !_isOpen) {
      _openDropdown();
    } else if (!_focusNode.hasFocus && _isOpen) {
      _closeDropdown();
    }
  }

  void _openDropdown() {
    if (_isOpen) return;

    final RenderBox renderBox = context.findRenderObject() as RenderBox;
    final size = renderBox.size;
    final offset = renderBox.localToGlobal(Offset.zero);

    _overlayEntry = _buildOverlayEntry(context, size, offset);
    Overlay.of(context).insert(_overlayEntry!);
    setState(() => _isOpen = true);
  }

  void _closeDropdown() {
    if (!_isOpen) return;

    _overlayEntry?.remove();
    _overlayEntry = null;
    setState(() => _isOpen = false);
    _focusNode.unfocus();
  }

  OverlayEntry _buildOverlayEntry(
      BuildContext context, Size buttonSize, Offset buttonOffset) {
    final textTheme = Theme.of(context).textTheme;
    final size = MediaQuery.sizeOf(context);
    return OverlayEntry(
      builder: (context) => GestureDetector(
        onTap: () => _closeDropdown(),
        behavior: HitTestBehavior.translucent,
        child: CompositedTransformFollower(
          link: _layerLink,
          showWhenUnlinked: false,
          offset: Offset(0.0, buttonSize.height + 8),
          child: Align(
            alignment: Alignment.topLeft,
            child: Material(
              borderRadius: BorderRadius.circular(16),
              color: Colors.white,
              child: Container(
                width: buttonSize.width,
                constraints: BoxConstraints(maxHeight: size.height * 0.4),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(16),
                  color: Colors.white,
                  border: Border.all(
                    color: AppColors.lightBlue,
                    width: 1.5,
                  ),
                ),
                child: ListView.builder(
                  padding: EdgeInsets.zero,
                  shrinkWrap: true,
                  itemCount: widget.items.length,
                  itemBuilder: (context, index) {
                    final item = widget.items[index];
                    return InkWell(
                      onTap: () {
                        widget.onChanged?.call(item.value);
                        _closeDropdown();
                      },
                      child: Padding(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 16.0, vertical: 12),
                        child: Text(
                          item.label,
                          style: textTheme.ralewaySemiBold.copyWith(
                            fontSize: 12,
                            color: AppColors.navy,
                          ),
                        ),
                      ),
                    );
                  },
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    return CompositedTransformTarget(
      link: _layerLink,
      child: Focus(
        focusNode: _focusNode,
        child: GestureDetector(
          onTap: () {
            if (_isOpen) {
              _closeDropdown();
            } else {
              _openDropdown();
            }
          },
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 12),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: AppColors.lightBlue,
                width: 1.5,
              ),
              color: Colors.white,
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  widget.value != null
                      ? widget.items
                          .firstWhere((item) => item.value == widget.value)
                          .label
                      : widget.hint,
                  style: textTheme.ralewayLight.copyWith(
                      fontSize: 12,
                      color: widget.value != null
                          ? AppColors.navy
                          : AppColors.midBlue
                      ),
                ),
                const Padding(
                  padding: EdgeInsets.only(left: 10.0),
                  child: Icon(
                    Icons.keyboard_arrow_down,
                    color: AppColors.navy,
                    size: 24,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
