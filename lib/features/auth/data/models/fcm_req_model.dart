class FcmRequestModel {
  final String fcmDeviceId;
  final String token;

  FcmRequestModel({
    required this.fcmDeviceId,
    required this.token,
  });

  Map<String, dynamic> toJson() {
    return {
      'fcm_device_id': fcmDeviceId,
      'token': token,
    };
  }

  factory FcmRequestModel.fromJson(Map<String, dynamic> json) {
    return FcmRequestModel(
      fcmDeviceId: json['fcm_device_id'] ?? '',
      token: json['token'] ?? '',
    );
  }

  FcmRequestModel copyWith({
    String? fcmDeviceId,
    String? token,
  }) {
    return FcmRequestModel(
      fcmDeviceId: fcmDeviceId ?? this.fcmDeviceId,
      token: token ?? this.token,
    );
  }

  @override
  String toString() {
    return 'FcmRequestModel(fcmDeviceId: $fcmDeviceId, token: $token)';
  }
}
